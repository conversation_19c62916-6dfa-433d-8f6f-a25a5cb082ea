// Copyright (c) BlockVision, Inc. All rights reserved.
// SPDX-License-Identifier: Apache-2.0

package sui

import (
	"context"
	"fmt"
	"github.com/block-vision/sui-go-sdk/constant"
	"github.com/block-vision/sui-go-sdk/models"
	"github.com/block-vision/sui-go-sdk/utils"
	"testing"
)

var ctx = context.Background()
var cli = NewSuiClient(constant.BvMainnetEndpoint)

func TestOnReadSystemFromSui(t *testing.T) {
	t.Run("test on sui_getCheckpoint", func(t *testing.T) {
		rsp, err := cli.SuiGetCheckpoint(ctx, models.SuiGetCheckpointRequest{
			CheckpointID: "1628214",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getCheckpoints", func(t *testing.T) {
		rsp, err := cli.SuiGetCheckpoints(ctx, models.SuiGetCheckpointsRequest{
			Limit:           5,
			DescendingOrder: true,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		for _, checkpoint := range rsp.Data {
			utils.PrettyPrint(checkpoint)
		}
	})

	t.Run("test on sui_getLatestCheckpointSequenceNumber", func(t *testing.T) {
		rsp, err := cli.SuiGetLatestCheckpointSequenceNumber(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getReferenceGasPrice", func(t *testing.T) {
		rsp, err := cli.SuiXGetReferenceGasPrice(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getCommitteeInfo", func(t *testing.T) {
		rsp, err := cli.SuiXGetCommitteeInfo(ctx, models.SuiXGetCommitteeInfoRequest{
			Epoch: "39",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getReferenceGasPrice", func(t *testing.T) {
		rsp, err := cli.SuiXGetReferenceGasPrice(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getStakes", func(t *testing.T) {
		rsp, err := cli.SuiXGetStakes(ctx, models.SuiXGetStakesRequest{
			Owner: "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getStakesByIds", func(t *testing.T) {
		rsp, err := cli.SuiXGetStakesByIds(ctx, models.SuiXGetStakesByIdsRequest{
			StakedSuiIds: []string{"0x02cfd8057d8a499bcd936ba65efd65889e66874b3819cb251fe9b9799048f1ed"},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getLatestSuiSystemState", func(t *testing.T) {
		rsp, err := cli.SuiXGetLatestSuiSystemState(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getChainIdentifier", func(t *testing.T) {
		rsp, err := cli.SuiGetChainIdentifier(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getValidatorsApy", func(t *testing.T) {
		rsp, err := cli.SuiXGetValidatorsApy(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getProtocolConfig", func(t *testing.T) {
		rsp, err := cli.SuiGetProtocolConfig(ctx, models.SuiGetProtocolConfigRequest{})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})
}

func TestOnReadCoinFromSui(t *testing.T) {
	t.Run("test on suix_getBalance", func(t *testing.T) {
		rsp, err := cli.SuiXGetBalance(ctx, models.SuiXGetBalanceRequest{
			Owner:    "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
			CoinType: "0x2::sui::SUI",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getAllBalances", func(t *testing.T) {
		rsp, err := cli.SuiXGetAllBalance(ctx, models.SuiXGetAllBalanceRequest{
			Owner: "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getCoins", func(t *testing.T) {
		rsp, err := cli.SuiXGetCoins(ctx, models.SuiXGetCoinsRequest{
			Owner:    "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
			CoinType: "0x2::sui::SUI",
			Limit:    5,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getAllCoins", func(t *testing.T) {
		rsp, err := cli.SuiXGetAllCoins(ctx, models.SuiXGetAllCoinsRequest{
			Owner: "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
			Limit: 5,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getCoinMetadata", func(t *testing.T) {
		rsp, err := cli.SuiXGetCoinMetadata(ctx, models.SuiXGetCoinMetadataRequest{
			CoinType: "0x06864a6f921804860930db6ddbe2e16acdf8504495ea7481637a1c8b9a8fe54b::cetus::CETUS",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getTotalSupply", func(t *testing.T) {
		rsp, err := cli.SuiXGetTotalSupply(ctx, models.SuiXGetTotalSupplyRequest{
			CoinType: "0x06864a6f921804860930db6ddbe2e16acdf8504495ea7481637a1c8b9a8fe54b::cetus::CETUS",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})
}

func TestOnReadTransactionFromSui(t *testing.T) {
	t.Run("test on sui_getTotalTransactionBlocks", func(t *testing.T) {
		rsp, err := cli.SuiGetTotalTransactionBlocks(ctx)

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getTransactionBlock", func(t *testing.T) {
		rsp, err := cli.SuiGetTransactionBlock(ctx, models.SuiGetTransactionBlockRequest{
			Digest: "2LYaFDf5oU64xguKAjSiH7TarPSkxc35sN6rPc8RsoWf",
			Options: models.SuiTransactionBlockOptions{
				ShowInput:    true,
				ShowRawInput: true,
				ShowEffects:  true,
				ShowEvents:   true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_multiGetTransactionBlocks", func(t *testing.T) {
		rsp, err := cli.SuiMultiGetTransactionBlocks(ctx, models.SuiMultiGetTransactionBlocksRequest{
			Digests: []string{"2LYaFDf5oU64xguKAjSiH7TarPSkxc35sN6rPc8RsoWf", "rHrHjircrKMRP5fRb6YW7ez2cdL3JhQhzoZDv4THEFk"},
			Options: models.SuiTransactionBlockOptions{
				ShowInput:    true,
				ShowRawInput: true,
				ShowEffects:  true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		for _, transactionBlock := range rsp {
			utils.PrettyPrint(*transactionBlock)
		}
	})

	t.Run("test on suix_queryTransactionBlocks", func(t *testing.T) {
		rsp, err := cli.SuiXQueryTransactionBlocks(ctx, models.SuiXQueryTransactionBlocksRequest{
			SuiTransactionBlockResponseQuery: models.SuiTransactionBlockResponseQuery{
				TransactionFilter: models.TransactionFilter{
					"FromAddress": "0x02bcc205ccf48ac87f081f907ddbd46de66f847afbb6a8b11801240132f4eec5",
				},
				Options: models.SuiTransactionBlockOptions{
					ShowInput:    true,
					ShowRawInput: true,
					ShowEffects:  true,
				},
			},
			Limit:           5,
			DescendingOrder: false,
		})
		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		for _, transactionBlock := range rsp.Data {
			utils.PrettyPrint(transactionBlock)
		}
	})
}

func TestOnReadObjectFromSui(t *testing.T) {
	t.Run("test on sui_getObject", func(t *testing.T) {
		rsp, err := cli.SuiGetObject(ctx, models.SuiGetObjectRequest{
			ObjectId: "0x02cfd8057d8a499bcd936ba65efd65889e66874b3819cb251fe9b9799048f1ed",
			Options: models.SuiObjectDataOptions{
				ShowContent:             true,
				ShowDisplay:             true,
				ShowType:                true,
				ShowBcs:                 true,
				ShowOwner:               true,
				ShowPreviousTransaction: true,
				ShowStorageRebate:       true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on suix_getOwnedObjects", func(t *testing.T) {
		suiObjectResponseQuery := models.SuiObjectResponseQuery{
			Options: models.SuiObjectDataOptions{
				ShowType:                true,
				ShowContent:             true,
				ShowBcs:                 true,
				ShowOwner:               true,
				ShowPreviousTransaction: true,
				ShowStorageRebate:       true,
				ShowDisplay:             true,
			},
		}

		rsp, err := cli.SuiXGetOwnedObjects(ctx, models.SuiXGetOwnedObjectsRequest{
			Address: "0xd939e3fe7ea4d503f84767dca0c58b7ec1c71f085638a4c0611aa64aa71b5fcf",
			Query:   suiObjectResponseQuery,
			Limit:   5,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_multiGetObjects", func(t *testing.T) {
		rsp, err := cli.SuiMultiGetObjects(ctx, models.SuiMultiGetObjectsRequest{
			ObjectIds: []string{"0x02cfd8057d8a499bcd936ba65efd65889e66874b3819cb251fe9b9799048f1ed"},
			Options: models.SuiObjectDataOptions{
				ShowContent:             true,
				ShowDisplay:             true,
				ShowType:                true,
				ShowBcs:                 true,
				ShowOwner:               true,
				ShowPreviousTransaction: true,
				ShowStorageRebate:       true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)

	})

	t.Run("test on suix_getDynamicFields", func(t *testing.T) {
		rsp, err := cli.SuiXGetDynamicField(ctx, models.SuiXGetDynamicFieldRequest{
			ObjectId: "0x02cfd8057d8a499bcd936ba65efd65889e66874b3819cb251fe9b9799048f1ed",
			Limit:    5,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)

	})

	t.Run("test on sui_tryGetPastObject", func(t *testing.T) {
		rsp, err := cli.SuiTryGetPastObject(ctx, models.SuiTryGetPastObjectRequest{
			ObjectId: "0x02cfd8057d8a499bcd936ba65efd65889e66874b3819cb251fe9b9799048f1ed",
			Version:  9636,
			Options: models.SuiObjectDataOptions{
				ShowContent:             true,
				ShowDisplay:             true,
				ShowType:                true,
				ShowBcs:                 true,
				ShowOwner:               true,
				ShowPreviousTransaction: true,
				ShowStorageRebate:       true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)

	})

	t.Run("test on sui_tryMultiGetPastObjects", func(t *testing.T) {
		rsp, err := cli.SuiTryMultiGetPastObjects(ctx, models.SuiTryMultiGetPastObjectsRequest{
			MultiGetPastObjects: []*models.PastObject{
				{
					ObjectId: "0xfe3e114168d65ca9c86e43ce0f8dc4f8e0fa5a03634a4c6bf292679f6d73ec72",
					Version:  "22945798",
				},
				{
					ObjectId: "0xbf67e84fef313e6f1756411b095ba07868804852c939691b300a7e1e45d0251f",
					Version:  "23119685",
				},
			},
			Options: models.SuiObjectDataOptions{
				ShowContent:             true,
				ShowDisplay:             true,
				ShowType:                true,
				ShowBcs:                 true,
				ShowOwner:               true,
				ShowPreviousTransaction: true,
				ShowStorageRebate:       true,
			},
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)

	})
}

func TestOnReadEventFromSui(t *testing.T) {
	t.Run("test on sui_getEvents", func(t *testing.T) {
		rsp, err := cli.SuiGetEvents(ctx, models.SuiGetEventsRequest{
			Digest: "4ErUvWjWdXY5zdVkRCqgQFJZQDTgJmHo55RFJW2FWcs2",
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		for _, event := range rsp {
			utils.PrettyPrint(*event)
		}
	})

	t.Run("test on suix_queryEvents", func(t *testing.T) {
		rsp, err := cli.SuiXQueryEvents(ctx, models.SuiXQueryEventsRequest{
			SuiEventFilter: models.EventFilterByMoveEventType{
				MoveEventType: "0x3::validator::StakingRequestEvent",
			},
			Limit:           5,
			DescendingOrder: false,
		})

		if err != nil {
			t.Error(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})
}

func TestOnReadMoveDataFromSui(t *testing.T) {
	t.Run("test on sui_getMoveFunctionArgTypes", func(t *testing.T) {
		rsp, err := cli.SuiGetMoveFunctionArgTypes(ctx, models.GetMoveFunctionArgTypesRequest{
			Package:  "0x9fe1780ac27ec50c9c441fb31822f5c148f841f09ee455c6a0daf7c634a30a27",
			Module:   "aifrens",
			Function: "claim",
		})

		if err != nil {
			fmt.Println(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getNormalizedMoveModulesByPackage", func(t *testing.T) {
		rsp, err := cli.SuiGetNormalizedMoveModulesByPackage(ctx, models.GetNormalizedMoveModulesByPackageRequest{
			Package: "0x9fe1780ac27ec50c9c441fb31822f5c148f841f09ee455c6a0daf7c634a30a27",
		})

		if err != nil {
			fmt.Println(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getNormalizedMoveModule", func(t *testing.T) {
		rsp, err := cli.SuiGetNormalizedMoveModule(ctx, models.GetNormalizedMoveModuleRequest{
			Package:    "0x9fe1780ac27ec50c9c441fb31822f5c148f841f09ee455c6a0daf7c634a30a27",
			ModuleName: "aifrens",
		})

		if err != nil {
			fmt.Println(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getNormalizedMoveStruct", func(t *testing.T) {
		rsp, err := cli.SuiGetNormalizedMoveStruct(ctx, models.GetNormalizedMoveStructRequest{
			Package:    "0x9fe1780ac27ec50c9c441fb31822f5c148f841f09ee455c6a0daf7c634a30a27",
			ModuleName: "aifrens",
			StructName: "AifrensPool",
		})

		if err != nil {
			fmt.Println(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})

	t.Run("test on sui_getNormalizedMoveFunction", func(t *testing.T) {
		rsp, err := cli.SuiGetNormalizedMoveFunction(ctx, models.GetNormalizedMoveFunctionRequest{
			Package:      "0x9fe1780ac27ec50c9c441fb31822f5c148f841f09ee455c6a0daf7c634a30a27",
			ModuleName:   "aifrens",
			FunctionName: "claim",
		})

		if err != nil {
			fmt.Println(err.Error())
			t.FailNow()
		}

		utils.PrettyPrint(rsp)
	})
}
