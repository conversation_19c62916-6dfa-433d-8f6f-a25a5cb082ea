// Copyright (c) BlockVision, Inc. All rights reserved.
// SPDX-License-Identifier: Apache-2.0

package sui

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/block-vision/sui-go-sdk/common/httpconn"
	"github.com/tidwall/gjson"
)

// BaseRequestHandler 统一的请求处理器，消除所有API文件中的重复代码
type BaseRequestHandler struct {
	conn *httpconn.HttpConn
}

// NewBaseRequestHandler 创建新的基础请求处理器
func NewBaseRequestHandler(conn *httpconn.HttpConn) *BaseRequestHandler {
	return &BaseRequestHandler{
		conn: conn,
	}
}

// ExecuteRequest 统一的请求执行方法
func (h *BaseRequestHandler) ExecuteRequest(ctx context.Context, method string, params []interface{}, result interface{}) error {
	respBytes, err := h.conn.Request(ctx, httpconn.Operation{
		Method: method,
		Params: params,
	})
	if err != nil {
		return fmt.Errorf("%s request failed: %w", method, err)
	}

	return h.parseResponse(respBytes, method, result)
}

// ExecuteRequestWithValidation 带验证的请求执行方法
func (h *BaseRequestHandler) ExecuteRequestWithValidation(ctx context.Context, method string, params []interface{}, req interface{}, result interface{}) error {
	if err := validate.ValidateStruct(req); err != nil {
		return fmt.Errorf("validation failed for %s: %w", method, err)
	}
	return h.ExecuteRequest(ctx, method, params, result)
}

// ExecuteSimpleRequest 执行简单请求（返回基本类型）
func (h *BaseRequestHandler) ExecuteSimpleRequest(ctx context.Context, method string, params []interface{}) (gjson.Result, error) {
	respBytes, err := h.conn.Request(ctx, httpconn.Operation{
		Method: method,
		Params: params,
	})
	if err != nil {
		return gjson.Result{}, fmt.Errorf("%s request failed: %w", method, err)
	}

	parsedJson := gjson.ParseBytes(respBytes)
	if parsedJson.Get("error").Exists() {
		return gjson.Result{}, errors.New(parsedJson.Get("error").String())
	}

	return parsedJson.Get("result"), nil
}

// parseResponse 统一的响应解析方法
func (h *BaseRequestHandler) parseResponse(respBytes []byte, method string, result interface{}) error {
	parsedJson := gjson.ParseBytes(respBytes)
	if parsedJson.Get("error").Exists() {
		return errors.New(parsedJson.Get("error").String())
	}

	resultData := parsedJson.Get("result")
	if !resultData.Exists() {
		return fmt.Errorf("no result field in %s response", method)
	}

	// 对于字符串类型的直接返回
	if strPtr, ok := result.(*string); ok {
		*strPtr = resultData.String()
		return nil
	}

	// 对于uint64类型的直接返回
	if uint64Ptr, ok := result.(*uint64); ok {
		*uint64Ptr = resultData.Uint()
		return nil
	}

	// 对于复杂类型使用JSON反序列化
	var jsonData string
	if resultData.IsObject() || resultData.IsArray() {
		jsonData = resultData.Raw
	} else {
		jsonData = resultData.String()
	}

	err := json.Unmarshal([]byte(jsonData), result)
	if err != nil {
		return fmt.Errorf("unmarshal %s response error: %w, response: %s", method, err, string(respBytes))
	}

	return nil
}
