package constant

const (
	BvTestnetEndpoint  = "https://sui-testnet-endpoint.blockvision.org"
	BvMainnetEndpoint  = "https://sui-mainnet-endpoint.blockvision.org"
	SuiTestnetEndpoint = "https://fullnode.testnet.sui.io"
	SuiMainnetEndpoint = "https://fullnode.mainnet.sui.io"

	WssBvTestnetEndpoint  = "wss://sui-testnet-endpoint.blockvision.org/websocket"
	WssBvMainnetEndpoint  = "wss://sui-mainnet-endpoint.blockvision.org/websocket"
	WssSuiTestnetEndpoint = "wss://fullnode.testnet.sui.io"
	WssSuiMainnetEndpoint = "wss://fullnode.mainnet.sui.io"

	FaucetTestnetEndpoint  = "https://faucet.testnet.sui.io/v1/gas"
	FaucetDevnetEndpoint   = "https://faucet.devnet.sui.io/v1/gas"
	FaucetLocalnetEndpoint = "http://127.0.0.1:9123/gas"
)

const (
	SuiMainnet  = "mainnet"
	SuiTestnet  = "testnet"
	SuiDevnet   = "devnet"
	SuiLocalnet = "localnet"
)
