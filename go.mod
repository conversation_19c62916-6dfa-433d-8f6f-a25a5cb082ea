module github.com/block-vision/sui-go-sdk

go 1.21

toolchain go1.24.1

require (
	github.com/btcsuite/btcutil v1.0.2
	github.com/cosmos/go-bip39 v1.0.0
	github.com/fardream/go-bcs v0.7.0
	github.com/go-playground/validator/v10 v10.12.0
	github.com/google/go-cmp v0.7.0
	github.com/gorilla/websocket v1.5.0
	github.com/jinzhu/copier v0.4.0
	github.com/machinebox/graphql v0.2.2
	github.com/mr-tron/base58 v1.2.0
	github.com/samber/lo v1.49.1
	github.com/stretchr/testify v1.10.0
	github.com/tidwall/gjson v1.14.4
	golang.org/x/crypto v0.23.0
	golang.org/x/time v0.5.0
)

require (
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/leodido/go-urn v1.2.2 // indirect
	github.com/matryer/is v1.4.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	golang.org/x/sys v0.20.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
